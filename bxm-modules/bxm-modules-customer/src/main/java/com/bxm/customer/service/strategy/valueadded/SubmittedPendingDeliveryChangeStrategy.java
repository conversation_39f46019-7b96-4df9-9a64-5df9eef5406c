package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.StatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 已提交待交付状态变更策略
 *
 * 处理从"已提交待交付"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. SUBMITTED_PENDING_DELIVERY -> PENDING_CONFIRMATION (正常交付完成)
 * 2. SUBMITTED_PENDING_DELIVERY -> DELIVERY_EXCEPTION (交付异常)
 * 3. SUBMITTED_PENDING_DELIVERY -> SAVED_PENDING_SUBMIT (退回待提交)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class SubmittedPendingDeliveryChangeStrategy implements StatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION ||
               targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT;
    }

    @Override
    public void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        log.info("Processing status change from SUBMITTED_PENDING_DELIVERY to {} for order: {}",
                request.getTargetStatus(), request.getDeliveryOrderNo());

        ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());

        if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            // 验证交付完成
            validateDeliveryCompleted(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION) {
            // 验证交付异常
            validateDeliveryException(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT) {
            // 验证退回待提交
            validateReturnToPendingSubmit(order, request);
        } else {
            throw new IllegalArgumentException("不支持从已提交待交付状态转换到: " + request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY;
    }

    /**
     * 验证交付完成
     */
    private void validateDeliveryCompleted(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限（交付人员权限）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证交付相关信息是否完整
        if (order.getRequirements() == null || order.getRequirements().trim().isEmpty()) {
            throw new IllegalArgumentException("交付要求不能为空");
        }

        // 验证业务部门信息
        if (order.getBusinessDeptId() == null) {
            throw new IllegalArgumentException("业务部门ID不能为空");
        }

        log.info("Delivery completion validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证交付异常
     */
    private void validateDeliveryException(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证异常原因必须提供
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("交付异常必须提供异常原因");
        }

        // 异常原因长度验证
        if (request.getReason().length() < 10) {
            throw new IllegalArgumentException("异常原因描述不能少于10个字符");
        }

        log.info("Delivery exception validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证退回待提交状态
     */
    private void validateReturnToPendingSubmit(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证退回原因
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("退回待提交状态必须提供原因");
        }

        log.info("Return to pending submit validation passed for order: {}", request.getDeliveryOrderNo());
    }
}
