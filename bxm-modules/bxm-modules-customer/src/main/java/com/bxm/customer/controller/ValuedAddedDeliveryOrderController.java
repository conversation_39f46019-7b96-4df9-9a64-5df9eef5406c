package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedItemTypeVO;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import com.bxm.customer.service.StateMachineManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 增值交付单Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/valuedAddedDeliveryOrder")
@Api(tags = "增值交付单管理")
public class ValuedAddedDeliveryOrderController extends BaseController {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @Autowired
    private StateMachineManager stateMachineManager;

    /**
     * 新增或更新增值交付单
     *
     * @param orderVO 增值交付单VO
     * @return 操作结果
     */
    @PostMapping("/upsert")
    @ApiOperation(value = "新增或更新增值交付单", notes = "支持新增和更新操作，根据ID或交付单编号自动判断")
    @Log(title = "Upsert value added delivery order", businessType = BusinessType.INSERT)
    public Result<ValueAddedDeliveryOrderVO> upsert(@Valid @RequestBody ValueAddedDeliveryOrderVO orderVO) {
        try {
            log.info("Upsert delivery order request: {}", orderVO.getCustomerName());
            // 调用服务层进行upsert操作
            ValueAddedDeliveryOrder result = valueAddedDeliveryOrderService.upsert(orderVO);
            // 转换为VO返回
            ValueAddedDeliveryOrderVO resultVO = new ValueAddedDeliveryOrderVO();
            BeanUtils.copyProperties(result, resultVO);
            log.info("Upsert delivery order success: {}", result.getDeliveryOrderNo());
            return Result.ok(resultVO);
        } catch (IllegalArgumentException e) {
            log.warn("Upsert delivery order validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Upsert delivery order failed for customer: {}", orderVO.getCustomerName(), e);
            return Result.fail("保存增值交付单失败");
        }
    }

    /**
     * 条件查询增值交付单
     * 所有条件在 service 层进行动态拼接
     */
    @GetMapping("/query")
    @ApiOperation(value = "增值交付单查询", notes = "按条件分页查询增值交付单；条件均为可选")
    @Log(title = "Query value added delivery order", businessType = BusinessType.OTHER)
    public Result<IPage<ValueAddedDeliveryOrderVO>> query(DeliveryOrderQuery query) {
        // 启动分页（从请求参数 pageNum/pageSize 注入）
        startPage();
        // 记录查询关键信息，避免日志过大
        log.info("Query delivery orders, deliveryOrderNo={}, customerName={}, itemTypeId={}, status={}",
                StringUtils.nvl(query.getDeliveryOrderNo(), ""),
                StringUtils.nvl(query.getCustomerName(), ""),
                query.getValueAddedItemTypeId(),
                StringUtils.nvl(query.getStatus(), ""));
        List<ValueAddedDeliveryOrderVO> list = valueAddedDeliveryOrderService.queryVO(query);

        // 创建分页结果对象
        IPage<ValueAddedDeliveryOrderVO> page = new Page<>(
            query.getPageNum() != null ? query.getPageNum() : 1,
            query.getPageSize() != null ? query.getPageSize() : 10
        );
        page.setRecords(list);

        // 设置总数（从PageHelper获取）
        if (list instanceof com.github.pagehelper.Page) {
            com.github.pagehelper.Page<?> pageInfo = (com.github.pagehelper.Page<?>) list;
            page.setTotal(pageInfo.getTotal());
        }

        return Result.ok(page);
    }

    /**
     * 根据交付单编号查询增值交付单
     *
     * @param deliveryOrderNo 交付单编号
     * @return 查询结果
     */
    @GetMapping("/getByOrderNo/{deliveryOrderNo}")
    @ApiOperation(value = "根据交付单编号查询", notes = "根据交付单编号查询增值交付单详情")
    @Log(title = "Get delivery order by order number", businessType = BusinessType.OTHER)
    public Result<ValueAddedDeliveryOrderVO> getByOrderNo(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Query delivery order by order number: {}", deliveryOrderNo);
            ValueAddedDeliveryOrder order = valueAddedDeliveryOrderService.getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                return Result.fail("未找到对应的增值交付单");
            }
            ValueAddedDeliveryOrderVO resultVO = new ValueAddedDeliveryOrderVO();
            BeanUtils.copyProperties(order, resultVO);
            return Result.ok(resultVO);
        } catch (Exception e) {
            log.error("Query delivery order failed for order number: {}", deliveryOrderNo, e);
            return Result.fail("查询增值交付单失败");
        }
    }

    /**
     * 生成交付单编号
     * 编号规则：VAD + yyMMddHHmmsss + 3位随机码，总长度19位
     */
    @GetMapping("/genDeliveryOrderNo")
    @ApiOperation(value = "生成交付单编号", notes = "生成唯一的增值交付单编号，格式：VAD+时间戳到毫秒+随机码，总长度19位")
    @Log(title = "Generate delivery order number", businessType = BusinessType.OTHER)
    public Result<String> genDeliveryOrderNo() {
        try {
            // 生成时间戳部分：yyMMddHHmmsss格式（13位）
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmsss"));
            // 生成3位随机码
            String randomCode = StringUtils.generateRandomCode(3);
            // 组合生成最终编号
            String deliveryOrderNo = "VAD" + timestamp + randomCode;
            log.info("Generated delivery order number: {}", deliveryOrderNo);
            return Result.ok(deliveryOrderNo);
        } catch (Exception e) {
            log.error("Failed to generate delivery order number", e);
            return Result.fail("生成交付单编号失败");
        }
    }

    /**
     * 查询增值事项类型列表
     *
     * @return 增值事项类型列表
     */
    @GetMapping("/listItemType")
    @ApiOperation(value = "查询增值事项类型列表", notes = "获取所有可用的增值事项类型")
    @Log(title = "List value added item types", businessType = BusinessType.OTHER)
    public Result<List<ValueAddedItemTypeVO>> listItemType() {
        try {
            log.info("Query value added item type list");
            List<ValueAddedItemTypeVO> itemTypes = valueAddedItemTypeService.listItemTypeVO();
            log.info("Query value added item type list success, count: {}", itemTypes.size());
            return Result.ok(itemTypes);
        } catch (Exception e) {
            log.error("Query value added item type list failed", e);
            return Result.fail("查询增值事项类型列表失败");
        }
    }

    /**
     * 修改增值交付单状态
     *
     * @param request 状态变更请求
     * @return 操作结果
     */
    @PostMapping("/changeStatus")
    @ApiOperation(value = "修改增值交付单状态", notes = "通过状态机管理器修改交付单状态，包含完整的业务验证")
    @Log(title = "Change delivery order status", businessType = BusinessType.UPDATE)
    public Result<String> changeStatus(@Valid @RequestBody StatusChangeRequestDTO request) {
        try {
            log.info("Change status request for order: {} to status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());

            // 调用服务层进行状态变更
            valueAddedDeliveryOrderService.changeStatus(request);

            log.info("Change status success for order: {} to status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());
            return Result.ok("状态修改成功");
        } catch (IllegalArgumentException e) {
            log.warn("Change status validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Change status failed for order: {}",
                    request != null ? request.getDeliveryOrderNo() : "unknown", e);
            return Result.fail("状态修改失败");
        }
    }

    /**
     * 获取指定交付单的可用状态列表
     *
     * @param deliveryOrderNo 交付单编号
     * @return 可用状态列表
     */
    @GetMapping("/availableStatuses/{deliveryOrderNo}")
    @ApiOperation(value = "获取可用状态列表", notes = "根据当前状态获取所有可以转换到的目标状态")
    @Log(title = "Get available statuses", businessType = BusinessType.OTHER)
    public Result<List<ValueAddedDeliveryOrderStatus>> getAvailableStatuses(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Get available statuses for order: {}", deliveryOrderNo);

            List<ValueAddedDeliveryOrderStatus> availableStatuses =
                    valueAddedDeliveryOrderService.getAvailableStatuses(deliveryOrderNo);

            log.info("Get available statuses success for order: {}, count: {}",
                    deliveryOrderNo, availableStatuses.size());
            return Result.ok(availableStatuses);

        } catch (IllegalArgumentException e) {
            log.warn("Get available statuses validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Get available statuses failed for order: {}", deliveryOrderNo, e);
            return Result.fail("获取可用状态列表失败");
        }
    }

    /**
     * 获取状态机策略覆盖情况（调试接口）
     *
     * @return 策略覆盖情况
     */
    @GetMapping("/debug/strategyCoverage")
    @ApiOperation(value = "获取状态机策略覆盖情况", notes = "调试接口，用于检查所有状态转换策略的覆盖情况")
    @Log(title = "Get strategy coverage", businessType = BusinessType.OTHER)
    public Result<String> getStrategyCoverage() {
        try {
            log.info("Getting strategy coverage information");
            String coverage = stateMachineManager.getStrategyCoverage();
            return Result.ok(coverage);
        } catch (Exception e) {
            log.error("Failed to get strategy coverage", e);
            return Result.fail("获取策略覆盖情况失败");
        }
    }
}
