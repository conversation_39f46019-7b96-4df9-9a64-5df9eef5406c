package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.ValueAddedPeriodMonth;
import com.bxm.customer.mapper.ValueAddedPeriodMonthMapper;
import com.bxm.customer.service.IValueAddedPeriodMonthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 增值期间月度表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class ValueAddedPeriodMonthServiceImpl extends ServiceImpl<ValueAddedPeriodMonthMapper, ValueAddedPeriodMonth>
        implements IValueAddedPeriodMonthService {

    @Override
    public boolean checkPeriodExists(Long businessTopDeptId, String creditCode, Integer periodStart, Integer periodEnd) {
        try {
            LambdaQueryWrapper<ValueAddedPeriodMonth> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ValueAddedPeriodMonth::getBusinessTopDeptId, businessTopDeptId)
                       .eq(ValueAddedPeriodMonth::getCreditCode, creditCode)
                       .ge(ValueAddedPeriodMonth::getPeriod, periodStart)
                       .le(ValueAddedPeriodMonth::getPeriod, periodEnd);

            Long count = count(queryWrapper);
            boolean exists = count != null && count > 0;
            
            log.debug("ValueAddedPeriodMonth query result: businessTopDeptId={}, creditCode={}, periodRange=[{},{}], count={}", businessTopDeptId, creditCode, periodStart, periodEnd, count);
            
            return exists;
        } catch (Exception e) {
            log.error("Error checking ValueAddedPeriodMonth table", e);
            throw new RuntimeException("查询增值账期表时发生错误", e);
        }
    }
}
