package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.domain.ValueAddedPeriodMonth;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.mapper.ValueAddedPeriodMonthMapper;
import com.bxm.customer.service.AccountPeriodValidationService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 账期校验服务实现类
 *
 * 处理增值交付单状态变更时的账期相关校验逻辑
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class AccountPeriodValidationServiceImpl implements AccountPeriodValidationService {

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private ValueAddedPeriodMonthMapper valueAddedPeriodMonthMapper;

    @Override
    public void validateAccountPeriodForSubmit(ValueAddedDeliveryOrder order) {
        try {
            log.info("Starting account period validation for order: {}, valueAddedItemTypeId: {}", 
                    order.getDeliveryOrderNo(), order.getValueAddedItemTypeId());

            // 1. 根据valueAddedItemTypeId获取itemName
            String itemName = getItemNameByTypeId(order.getValueAddedItemTypeId());
            if (itemName == null) {
                log.warn("Item name not found for valueAddedItemTypeId: {}", order.getValueAddedItemTypeId());
                return; // 如果找不到对应的事项名称，跳过校验
            }

            log.info("Found item name: {} for order: {}", itemName, order.getDeliveryOrderNo());

            // 2. 根据itemName执行不同的校验逻辑
            if ("补账".equals(itemName)) {
                validateSupplementAccount(order);
            } else if ("改账".equals(itemName)) {
                validateModifyAccount(order);
            } else {
                log.info("Item name '{}' does not require period validation, skipping", itemName);
            }

            log.info("Account period validation completed successfully for order: {}", order.getDeliveryOrderNo());

        } catch (IllegalArgumentException e) {
            log.error("Account period validation failed for order: {}, error: {}", 
                    order.getDeliveryOrderNo(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during account period validation for order: {}", 
                    order.getDeliveryOrderNo(), e);
            throw new RuntimeException("账期校验过程中发生系统异常", e);
        }
    }

    /**
     * 根据增值事项类型ID获取事项名称
     */
    private String getItemNameByTypeId(Integer valueAddedItemTypeId) {
        if (valueAddedItemTypeId == null) {
            return null;
        }

        try {
            ValueAddedItemType itemType = valueAddedItemTypeService.getById(valueAddedItemTypeId.longValue());
            return itemType != null ? itemType.getItemName() : null;
        } catch (Exception e) {
            log.error("Failed to get item name for valueAddedItemTypeId: {}", valueAddedItemTypeId, e);
            return null;
        }
    }

    /**
     * 校验补账类型：如果在账期范围内存在period记录，则报错
     */
    private void validateSupplementAccount(ValueAddedDeliveryOrder order) {
        log.info("Validating supplement account for order: {}", order.getDeliveryOrderNo());

        // 检查必要字段
        validateRequiredFields(order);

        // 查询CustomerServicePeriodMonth表
        boolean existsInCustomerService = checkPeriodExistsInCustomerServicePeriodMonth(
                order.getBusinessTopDeptId(), 
                order.getCreditCode(), 
                order.getAccountingPeriodStart(), 
                order.getAccountingPeriodEnd()
        );

        // 查询ValueAddedPeriodMonth表
        boolean existsInValueAdded = checkPeriodExistsInValueAddedPeriodMonth(
                order.getBusinessTopDeptId(), 
                order.getCreditCode(), 
                order.getAccountingPeriodStart(), 
                order.getAccountingPeriodEnd()
        );

        // 如果任一表中存在记录，则报错
        if (existsInCustomerService || existsInValueAdded) {
            String message = "当前存在部分账期不允许提交";
            log.warn("Supplement account validation failed for order: {}, reason: {}", 
                    order.getDeliveryOrderNo(), message);
            throw new IllegalArgumentException(message);
        }

        log.info("Supplement account validation passed for order: {}", order.getDeliveryOrderNo());
    }

    /**
     * 校验改账类型：如果在账期范围内不存在任何period记录，则报错
     */
    private void validateModifyAccount(ValueAddedDeliveryOrder order) {
        log.info("Validating modify account for order: {}", order.getDeliveryOrderNo());

        // 检查必要字段
        validateRequiredFields(order);

        // 查询CustomerServicePeriodMonth表
        boolean existsInCustomerService = checkPeriodExistsInCustomerServicePeriodMonth(
                order.getBusinessTopDeptId(), 
                order.getCreditCode(), 
                order.getAccountingPeriodStart(), 
                order.getAccountingPeriodEnd()
        );

        // 查询ValueAddedPeriodMonth表
        boolean existsInValueAdded = checkPeriodExistsInValueAddedPeriodMonth(
                order.getBusinessTopDeptId(), 
                order.getCreditCode(), 
                order.getAccountingPeriodStart(), 
                order.getAccountingPeriodEnd()
        );

        // 如果两个表中都不存在记录，则报错
        if (!existsInCustomerService && !existsInValueAdded) {
            String message = "当前账期不存在不允许提交";
            log.warn("Modify account validation failed for order: {}, reason: {}", 
                    order.getDeliveryOrderNo(), message);
            throw new IllegalArgumentException(message);
        }

        log.info("Modify account validation passed for order: {}", order.getDeliveryOrderNo());
    }

    /**
     * 校验必要字段
     */
    private void validateRequiredFields(ValueAddedDeliveryOrder order) {
        if (order.getBusinessTopDeptId() == null) {
            throw new IllegalArgumentException("业务顶级部门ID不能为空");
        }
        if (order.getCreditCode() == null || order.getCreditCode().trim().isEmpty()) {
            throw new IllegalArgumentException("统一社会信用代码不能为空");
        }
        if (order.getAccountingPeriodStart() == null) {
            throw new IllegalArgumentException("账期开始时间不能为空");
        }
        if (order.getAccountingPeriodEnd() == null) {
            throw new IllegalArgumentException("账期结束时间不能为空");
        }
    }

    /**
     * 检查CustomerServicePeriodMonth表中是否存在指定条件的记录
     */
    private boolean checkPeriodExistsInCustomerServicePeriodMonth(Long businessTopDeptId, String creditCode, 
                                                                  Integer periodStart, Integer periodEnd) {
        try {
            LambdaQueryWrapper<CustomerServicePeriodMonth> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CustomerServicePeriodMonth::getBusinessTopDeptId, businessTopDeptId)
                       .eq(CustomerServicePeriodMonth::getCreditCode, creditCode)
                       .ge(CustomerServicePeriodMonth::getPeriod, periodStart)
                       .le(CustomerServicePeriodMonth::getPeriod, periodEnd);

            Long count = customerServicePeriodMonthMapper.selectCount(queryWrapper);
            boolean exists = count != null && count > 0;
            
            log.debug("CustomerServicePeriodMonth query result: businessTopDeptId={}, creditCode={}, " +
                     "periodRange=[{},{}], count={}", businessTopDeptId, creditCode, periodStart, periodEnd, count);
            
            return exists;
        } catch (Exception e) {
            log.error("Error checking CustomerServicePeriodMonth table", e);
            throw new RuntimeException("查询客户服务账期表时发生错误", e);
        }
    }

    /**
     * 检查ValueAddedPeriodMonth表中是否存在指定条件的记录
     */
    private boolean checkPeriodExistsInValueAddedPeriodMonth(Long businessTopDeptId, String creditCode, 
                                                            Integer periodStart, Integer periodEnd) {
        try {
            LambdaQueryWrapper<ValueAddedPeriodMonth> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ValueAddedPeriodMonth::getBusinessTopDeptId, businessTopDeptId)
                       .eq(ValueAddedPeriodMonth::getCreditCode, creditCode)
                       .ge(ValueAddedPeriodMonth::getPeriod, periodStart)
                       .le(ValueAddedPeriodMonth::getPeriod, periodEnd);

            Long count = valueAddedPeriodMonthMapper.selectCount(queryWrapper);
            boolean exists = count != null && count > 0;
            
            log.debug("ValueAddedPeriodMonth query result: businessTopDeptId={}, creditCode={}, " +
                     "periodRange=[{},{}], count={}", businessTopDeptId, creditCode, periodStart, periodEnd, count);
            
            return exists;
        } catch (Exception e) {
            log.error("Error checking ValueAddedPeriodMonth table", e);
            throw new RuntimeException("查询增值账期表时发生错误", e);
        }
    }
}
