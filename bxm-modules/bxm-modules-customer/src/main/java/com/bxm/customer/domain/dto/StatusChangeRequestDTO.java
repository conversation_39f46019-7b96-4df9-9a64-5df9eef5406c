package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 状态变更请求DTO
 *
 * 用于增值交付单状态变更的请求参数封装
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("状态变更请求DTO")
public class StatusChangeRequestDTO {

    /**
     * 交付单编号
     */
    @NotBlank(message = "交付单编号不能为空")
    @Size(max = 50, message = "交付单编号长度不能超过50个字符")
    @ApiModelProperty(value = "交付单编号", required = true, example = "VAD2508051430001A1C")
    private String deliveryOrderNo;

    /**
     * 目标状态
     */
    @NotBlank(message = "目标状态不能为空")
    @Size(max = 50, message = "目标状态长度不能超过50个字符")
    @ApiModelProperty(value = "目标状态", required = true, example = "SAVED_PENDING_SUBMIT")
    private String targetStatus;

    /**
     * 变更原因
     */
    @Size(max = 500, message = "变更原因长度不能超过500个字符")
    @ApiModelProperty(value = "变更原因", example = "客户确认提交申请")
    private String reason;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    @ApiModelProperty(value = "操作人ID", required = true, example = "1001")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @Size(max = 100, message = "操作人姓名长度不能超过100个字符")
    @ApiModelProperty(value = "操作人姓名", example = "张三")
    private String operatorName;

    /**
     * 备注信息
     */
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    @ApiModelProperty(value = "备注信息", example = "系统自动状态变更")
    private String remark;

    /** 顶级业务部门id */
    @ApiModelProperty(value = "集团id")
    private Long businessTopDeptId;

    /** 统一社会信用代码 */
    @NotBlank(message = "统一社会信用代码不能为空")
    @Pattern(regexp = "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$", message = "统一社会信用代码格式不正确")
    @ApiModelProperty(value = "统一社会信用代码", required = true)
    private String creditCode;
}
