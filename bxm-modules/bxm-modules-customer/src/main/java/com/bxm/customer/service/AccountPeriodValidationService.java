package com.bxm.customer.service;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;

/**
 * 账期校验服务接口
 *
 * 用于处理增值交付单状态变更时的账期相关校验逻辑
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface AccountPeriodValidationService {

    /**
     * 校验补账和改账类型的账期规则
     *
     * 根据增值事项类型执行不同的校验逻辑：
     * 1. 补账：如果在指定账期范围内存在任何period记录，则抛出异常
     * 2. 改账：如果在指定账期范围内不存在任何period记录，则抛出异常
     *
     * @param order 增值交付单对象，包含businessTopDeptId、creditCode、accountingPeriodStart、accountingPeriodEnd、valueAddedItemTypeId等字段
     * @throws IllegalArgumentException 当校验失败时抛出，包含具体的错误信息
     * @throws RuntimeException 当系统异常时抛出
     */
    void validateAccountPeriodForSubmit(ValueAddedDeliveryOrder order);
}
